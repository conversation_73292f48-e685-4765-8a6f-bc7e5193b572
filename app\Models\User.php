<?php

namespace App\Models;

use App\Models\Notification\NotificationDb;
use App\Models\User\UserTenant;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Lara<PERSON>\Passport\HasApiTokens as PassportHasApiTokens;
use Laravel\Sanctum\HasApiTokens as SanctumHasApiTokens;
use Modules\Master\App\Models\UserPpjk;
use Spatie\Permission\Traits\HasRoles;
use OwenIt\Auditing\Contracts\Auditable;

//use Laravel\Passport\HasApiTokens;

/**
 * @OA\Schema(
 *     schema="User",
 *     type="object",
 *     title="User",
 *     description="User model",
 *     @OA\Property(
 *         property="id",
 *         type="integer",
 *         description="User ID",
 *         example=1
 *     ),
 *     @OA\Property(
 *         property="name",
 *         type="string",
 *         description="Name of the user",
 *         example="<PERSON>"
 *     ),
 *     @OA\Property(
 *         property="username",
 *         type="string",
 *         description="Username of the user",
 *         example="johndoe"
 *     ),
 *     @OA\Property(
 *         property="email",
 *         type="string",
 *         description="Email address of the user",
 *         example="<EMAIL>"
 *     ),
 *     @OA\Property(
 *         property="password",
 *         type="string",
 *         description="Hashed password",
 *         example="$2y$10$E4KlgJDqOLx7."
 *     ),
 *     @OA\Property(
 *         property="manager",
 *         type="integer",
 *         description="Manager flag (1 = manager, 0 = not a manager)",
 *         example=0
 *     ),
 *     @OA\Property(
 *         property="status",
 *         type="boolean",
 *         description="User active status",
 *         example=true
 *     ),
 *     @OA\Property(
 *         property="created_at",
 *         type="string",
 *         format="date-time",
 *         description="User creation timestamp",
 *         example="2024-10-25T08:30:00Z"
 *     ),
 *     @OA\Property(
 *         property="updated_at",
 *         type="string",
 *         format="date-time",
 *         description="User update timestamp",
 *         example="2024-10-25T09:30:00Z"
 *     ),
 *     @OA\Property(
 *         property="role_name",
 *         type="array",
 *         description="List of roles assigned to the user",
 *         @OA\Items(type="string"),
 *         example={"admin", "editor"}
 *     ),
 *     @OA\Property(
 *         property="tenant",
 *         description="Tenants related to the user",
 *         type="array",
 *         @OA\Items(ref="#/components/schemas/UserTenant")
 *     )
 * )
 */
/**
 * App\Models\User
 *
 * @property int $id
 * @property string $name
 * @property string $username
 * @property int $manager
 * @property string $email
 * @property string $password
 * @property int $permission_id
 * @property string $created_by
 * @property string|null $remember_token
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property bool $status
 * @property string|null $Flags
 * @property string|null $avatar
 * @property string|null $dateChangePassword
 * @property-read mixed $role_name
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\User\UserTenant> $handle
 * @property-read int|null $handle_count
 * @property-read \Illuminate\Notifications\DatabaseNotificationCollection<int, NotificationDb> $notifications
 * @property-read int|null $notifications_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Spatie\Permission\Models\Permission> $permissions
 * @property-read int|null $permissions_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Spatie\Permission\Models\Role> $roles
 * @property-read int|null $roles_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\User\UserTenant> $tenant
 * @property-read int|null $tenant_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Laravel\Sanctum\PersonalAccessToken> $tokens
 * @property-read int|null $tokens_count
 * @method static \Illuminate\Database\Eloquent\Builder|User newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|User newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|User permission($permissions)
 * @method static \Illuminate\Database\Eloquent\Builder|User query()
 * @method static \Illuminate\Database\Eloquent\Builder|User role($roles, $guard = null)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereAvatar($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereDateChangePassword($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereEmail($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereFlags($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereManager($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User wherePassword($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User wherePermissionId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereRememberToken($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereUsername($value)
 * @property int|null $ppjk_id
 * @property string $avatar_icon
 * @property string $show_notification
 * @property string $get_notification
 * @property string $show_new_feature
 * @property string|null $cherry_username
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \OwenIt\Auditing\Models\Audit> $audits
 * @property-read int|null $audits_count
 * @method static \Illuminate\Database\Eloquent\Builder|User whereAvatarIcon($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereCherryUsername($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereGetNotification($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User wherePpjkId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereShowNewFeature($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereShowNotification($value)
 * @property-read \Illuminate\Database\Eloquent\Collection<int, UserPpjk> $ppjkMaster
 * @property-read int|null $ppjk_master_count
 * @method static \Illuminate\Database\Eloquent\Builder|User withoutPermission($permissions)
 * @method static \Illuminate\Database\Eloquent\Builder|User withoutRole($roles, $guard = null)
 * @property-read \Illuminate\Database\Eloquent\Collection<int, UserTenant> $tenantHandle
 * @property-read int|null $tenant_handle_count
 * @mixin \Eloquent
 */
class User extends Authenticatable implements Auditable
{
    use \OwenIt\Auditing\Auditable;
    use Notifiable, HasRoles;

    // Use Sanctum's HasApiTokens
    use SanctumHasApiTokens;

    protected $connection = 'sqlsrv';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $guarded = [];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'ppjk_id' => 'int',
    ];

    /**
     * The attributes that should be hidden for arrays.
     *
     * @var array
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    protected $appends = [
        'role_name'
    ];

    public function getRoleNameAttribute()
    {
        return $this->roles()->pluck('name');
    }

    public function handle()
    {
        return $this->hasMany(UserTenant::class, "User_key");
    }

    public function tenant()
    {
        return $this->hasMany(UserTenant::class, "User_key");
    }

    public function ppjkMaster()
    {
        return $this->hasMany(UserPpjk::class, "UserId", "id");
    }

    /**
     * Get the entity's notifications.
     *
     * @return \Illuminate\Database\Eloquent\Relations\MorphMany
     */
    public function notifications()
    {
        return $this->morphMany(NotificationDb::class, 'notifiable')->latest();
    }

    /**
     * The channels the user receives notification broadcasts on.
     */
    public function receivesBroadcastNotificationsOn(): string
    {
        return 'user.' . $this->id;
    }

    /**
     * Find the user instance for the given username.
     */
    public function findForPassport(string $username): ?User
    {
        return $this->where('username', $username)->first();
    }

    public function tenantHandle(): HasMany
    {
        return $this->hasMany(UserTenant::class, "User_key", "id");
    }
}
