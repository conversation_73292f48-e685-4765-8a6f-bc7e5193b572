<?php

namespace App\Models;

use App\Models\Notification\NotificationDb;
use App\Models\User\UserTenant;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Lara<PERSON>\Passport\HasApiTokens as PassportHasApiTokens;
use Modules\Master\App\Models\UserPpjk;
use Spatie\Permission\Traits\HasRoles;
use OwenIt\Auditing\Contracts\Auditable;

/**
 *
 *
 * @property int $id
 * @property string $name
 * @property string $username
 * @property int $manager
 * @property string $email
 * @property string $password
 * @property int $permission_id
 * @property string $created_by
 * @property string|null $remember_token
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property int $status
 * @property string|null $Flags
 * @property string|null $avatar
 * @property string|null $dateChangePassword
 * @property int|null $ppjk_id
 * @property string $avatar_icon
 * @property string $show_notification
 * @property string $get_notification
 * @property string $show_new_feature
 * @property string|null $cherry_username
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \OwenIt\Auditing\Models\Audit> $audits
 * @property-read int|null $audits_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Laravel\Passport\Client> $clients
 * @property-read int|null $clients_count
 * @property-read mixed $role_name
 * @property-read \Illuminate\Database\Eloquent\Collection<int, UserTenant> $handle
 * @property-read int|null $handle_count
 * @property-read \Illuminate\Notifications\DatabaseNotificationCollection<int, NotificationDb> $notifications
 * @property-read int|null $notifications_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Spatie\Permission\Models\Permission> $permissions
 * @property-read int|null $permissions_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, UserPpjk> $ppjkMaster
 * @property-read int|null $ppjk_master_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Spatie\Permission\Models\Role> $roles
 * @property-read int|null $roles_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, UserTenant> $tenant
 * @property-read int|null $tenant_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Laravel\Passport\Token> $tokens
 * @property-read int|null $tokens_count
 * @method static \Illuminate\Database\Eloquent\Builder|PassportUser newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|PassportUser newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|PassportUser permission($permissions, $without = false)
 * @method static \Illuminate\Database\Eloquent\Builder|PassportUser query()
 * @method static \Illuminate\Database\Eloquent\Builder|PassportUser role($roles, $guard = null, $without = false)
 * @method static \Illuminate\Database\Eloquent\Builder|PassportUser whereAvatar($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PassportUser whereAvatarIcon($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PassportUser whereCherryUsername($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PassportUser whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PassportUser whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PassportUser whereDateChangePassword($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PassportUser whereEmail($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PassportUser whereFlags($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PassportUser whereGetNotification($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PassportUser whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PassportUser whereManager($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PassportUser whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PassportUser wherePassword($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PassportUser wherePermissionId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PassportUser wherePpjkId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PassportUser whereRememberToken($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PassportUser whereShowNewFeature($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PassportUser whereShowNotification($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PassportUser whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PassportUser whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PassportUser whereUsername($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PassportUser withoutPermission($permissions)
 * @method static \Illuminate\Database\Eloquent\Builder|PassportUser withoutRole($roles, $guard = null)
 * @property-read \Illuminate\Database\Eloquent\Collection<int, UserTenant> $tenantHandle
 * @property-read int|null $tenant_handle_count
 * @mixin \Eloquent
 */
class PassportUser extends Authenticatable implements Auditable
{
    use \OwenIt\Auditing\Auditable;
    use Notifiable, HasRoles;
    use PassportHasApiTokens;
    protected $table = 'users';

    protected $connection = 'sqlsrv';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $guarded = [];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'ppjk_id' => 'int',
    ];

    /**
     * The attributes that should be hidden for arrays.
     *
     * @var array
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    protected $appends = [
        'role_name'
    ];

    public function getRoleNameAttribute()
    {
        return $this->roles()->pluck('name');
    }

    public function handle()
    {
        return $this->hasMany(UserTenant::class, "User_key");
    }

    public function tenant()
    {
        return $this->hasMany(UserTenant::class, "User_key");
    }

    public function ppjkMaster()
    {
        return $this->hasMany(UserPpjk::class, "UserId", "id");
    }

    /**
     * Get the entity's notifications.
     *
     * @return \Illuminate\Database\Eloquent\Relations\MorphMany
     */
    public function notifications()
    {
        return $this->morphMany(NotificationDb::class, 'notifiable')->latest();
    }

    /**
     * The channels the user receives notification broadcasts on.
     */
    public function receivesBroadcastNotificationsOn(): string
    {
        return 'user.' . $this->id;
    }

    /**
     * Find the user instance for the given username.
     */
    public function findForPassport(string $username): ?PassportUser
    {
        return $this->where('username', $username)->first();
    }

    public function tenantHandle(): HasMany
    {
        return $this->hasMany(UserTenant::class, "User_key", "id");
    }
}
