<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use Modules\ExternalApi\App\Http\Controllers\AttachmentPathController;
use Modules\ExternalApi\App\Http\Controllers\MasterDataController;
use Modules\ExternalApi\App\Http\Controllers\ThirdParty\BoundedZoneTransactionController;
use Modules\ExternalApi\App\Http\Controllers\ThirdParty\CategoryItemController;
use Modules\ExternalApi\App\Http\Controllers\ThirdParty\DocumentStageController;
use Modules\ExternalApi\App\Http\Controllers\ThirdParty\ExternalAttachmentController;
use Modules\ExternalApi\App\Http\Controllers\ThirdParty\ExternalBcTypeController;
use Modules\ExternalApi\App\Http\Controllers\ThirdParty\ExternalBusinessPartnerController;
use Modules\ExternalApi\App\Http\Controllers\ThirdParty\ExternalImportController;
use Modules\ExternalApi\App\Http\Controllers\ThirdParty\ExternalImportInvoiceController;
use Modules\ExternalApi\App\Http\Controllers\ThirdParty\ExternalImportInvoiceDetailController;
use Modules\ExternalApi\App\Http\Controllers\ThirdParty\ExternalNotificationController;
use Modules\ExternalApi\App\Http\Controllers\ThirdParty\ExternalTenantHandleController;
use Modules\ExternalApi\App\Http\Controllers\ThirdParty\ExternalTradingController;
use Modules\ExternalApi\App\Http\Controllers\ThirdParty\ExternalTradingInvoiceController;
use Modules\ExternalApi\App\Http\Controllers\ThirdParty\ExternalTradingInvoiceDetailController;
use Modules\ExternalApi\App\Http\Controllers\TransactionController;

Route::middleware(['jwt.custom.verify'])->prefix('v1')->group(function () {
    Route::get('externalapi', fn(Request $request) => $request->user())->name('externalapi');
    Route::group(['prefix' => 'externalapi'], function () {
        Route::get("transaction", [TransactionController::class, 'index']);
        Route::get("transaction-detail", [TransactionController::class, 'show']);
        Route::get('tenant', [MasterDataController::class, 'tenant']);
        Route::get('vessel', [MasterDataController::class, 'vessel']);
        Route::get('jetty', [MasterDataController::class, 'jetty']);
        Route::get('jetty-location', [MasterDataController::class, 'jettyLocation']);
    });
});

Route::middleware(["custom.passport", "api.logger"])->prefix("v1")->group(function () {
    Route::get('bc-type', [ExternalBcTypeController::class, 'index']);
    Route::get('tenant', [ExternalTenantHandleController::class, 'index']);
    Route::get('category-item', [CategoryItemController::class, 'index']);
    Route::get("document-stage", [DocumentStageController::class, "index"]);

    Route::post("bounded-zone-transaction", [BoundedZoneTransactionController::class, "index"]);

    Route::get("attachment/{attachment}", [ExternalAttachmentController::class, 'show']);
    Route::put("attachment/{attachment}", [ExternalAttachmentController::class, 'update']);
    Route::post("attachment", [ExternalAttachmentController::class, 'store']);
    Route::delete("attachment", [ExternalAttachmentController::class, 'destroy']);

    Route::post("draft-attachment/{id}", [ExternalNotificationController::class, "store"]);

    Route::apiResources([
        "kb-trading" => ExternalTradingController::class,
        "business-partner" => ExternalBusinessPartnerController::class,
        "kb-trading-inv" => ExternalTradingInvoiceController::class,
        "kb-trading-inv-detail" => ExternalTradingInvoiceDetailController::class,
        "kb-import" => ExternalImportController::class,
        "kb-import-inv" => ExternalImportInvoiceController::class,
        "kb-import-inv-detail" => ExternalImportInvoiceDetailController::class,
        // "attachment" => ExternalAttachmentController::class,
        "notification" => ExternalNotificationController::class
    ]);
});
