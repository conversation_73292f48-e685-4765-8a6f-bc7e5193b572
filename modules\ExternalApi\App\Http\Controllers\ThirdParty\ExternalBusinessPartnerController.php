<?php

namespace Modules\ExternalApi\App\Http\Controllers\ThirdParty;

use App\Http\Controllers\Controller;
use App\Models\Master\BP;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Modules\ExternalApi\App\Http\Requests\StoreBusinessPartnerRequest;
use Modules\ExternalApi\App\Http\Requests\UpdateBusinessPartnerRequest;

class ExternalBusinessPartnerController extends Controller
{


    /**
     * Retrieves a list of business partners with an active status, ordered by name in ascending order.
     *
     * @group Master/Business Partner
     *
     * @responseFile scenario="success" resources/responses/master/bp.json
     * @responseFile status=401 scenario="invalid_token_format" resources/responses/passport/invalid_token_format.json
     * @responseFile status=401 scenario="token_expired" resources/responses/passport/token_expired.json
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function index()
    {
        try {
            $query = BP::select([
                "DocEntry as id",
                "Name as bp_name",
                "Address as address",
                "Npwp1 as npwp",
                "Npwp2 as nitku",
            ])
                ->where('Status', '=', 'Y')
                ->orderBY('Name', 'ASC')
                ->get();

            return $this->responseApi([
                "data" => $query
            ], "Business Partner retrieved successfully");
        } catch (\Exception $exception) {
            return $this->responseApi(
                [
                    "data" => [],
                ],
                $exception->getMessage(),
                $exception->getCode(),
                "error"
            );
        }
    }

    public function show($id)
    {
        try {
            $query = BP::select([
                "DocEntry as id",
                "Name as bp_name",
                "Address as address",
                "Npwp1 as npwp",
                "Npwp2 as nitku",
            ])
                ->where('Status', '=', 'Y')
                ->where('DocEntry', '=', $id)
                ->first();

            return $this->responseApi([
                "data" => $query
            ], "Business Partner retrieved successfully");
        } catch (\Exception $exception) {
            return $this->responseApi(
                [
                    "data" => [],
                ],
                $exception->getMessage(),
                $exception->getCode(),
                "error"
            );
        }
    }

    public function store(StoreBusinessPartnerRequest $request)
    {
        try {
            $data = BP::create([
                'Name' => $request->Name,
                'Address' => $request->Address,
                'Status' => 'Y',
                'Tenant' => null,
                'Npwp1' => (!empty($request->NPWP)) ? $request->NPWP : null,
                'Npwp2' => (!empty($request->NITKU)) ? $request->NITKU : null,
                'Created_by' => Auth::user()->username,
                'created_at' => Carbon::now(),
            ]);

            return $this->responseApi([
                "data" => $data
            ], "Business Partner retrieved successfully");
        } catch (\Exception $exception) {
            return $this->responseApi(
                [
                    "data" => [],
                ],
                $exception->getMessage(),
                $exception->getCode(),
                "error"
            );
        }
    }

    public function update(UpdateBusinessPartnerRequest $request, $id)
    {
        try {
            $data = BP::where('DocEntry', $id)->first();
            if ($data) {
                if ($data->Created_by != Auth::user()->username) {
                    return $this->responseApi(
                        [
                            "data" => [],
                        ],
                        "Not authorize to update this !",
                        401,
                        "error"
                    );
                }
                $data->Name = $request->Name;
                $data->Address = $request->Address;
                $data->Npwp1 = (!empty($request->NPWP)) ? $request->NPWP : null;
                $data->Npwp2 = (!empty($request->NITKU)) ? $request->NITKU : null;
                $data->updated_at = Carbon::now();
                $data->save();

                return $this->responseApi([
                    "data" => $data
                ], "Business Partner retrieved successfully");
            } else {
                return $this->responseApi(
                    [
                        "data" => [],
                    ],
                    "Business Partner not found!",
                    404,
                    "error"
                );
            }
        } catch (\Exception $exception) {
            return $this->responseApi(
                [
                    "data" => [],
                ],
                $exception->getMessage(),
                $exception->getCode(),
                "error"
            );
        }
    }
    public function destroy($id)
    {
        try {
            $data = BP::where('DocEntry', $id)->first();
            if ($data) {
                $data->Status = 'N';
                $data->save;

                return $this->responseApi([
                    "data" => $data
                ], "Business Partner retrieved successfully");
            } else {
                return $this->responseApi(
                    [
                        "data" => [],
                    ],
                    "Business Partner not found!",
                    404,
                    "error"
                );
            }
        } catch (\Exception $exception) {
            return $this->responseApi(
                [
                    "data" => [],
                ],
                $exception->getMessage(),
                $exception->getCode(),
                "error"
            );
        }
    }
}
