<?php

namespace Modules\ExternalApi\App\Http\Controllers\ThirdParty;

use App\Http\Controllers\Controller;
use App\Models\Master\BP;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ExternalBusinessPartnerController extends Controller
{


    /**
     * Retrieves a list of business partners with an active status, ordered by name in ascending order.
     *
     * @group Master/Business Partner
     *
     * @responseFile scenario="success" resources/responses/master/bp.json
     * @responseFile status=401 scenario="invalid_token_format" resources/responses/passport/invalid_token_format.json
     * @responseFile status=401 scenario="token_expired" resources/responses/passport/token_expired.json
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function index()
    {
        try {
            $query = BP::select([
                "DocEntry as id",
                "Name as bp_name"
            ])
                ->where('Status', '=', 'Y')
                ->orderBY('Name', 'ASC')
                ->get();

            return $this->responseApi([
                "data" => $query
            ], "Business Partner retrieved successfully");
        } catch (\Exception $exception) {
            return $this->responseApi(
                [
                    "data" => [],
                ],
                $exception->getMessage(),
                $exception->getCode(),
                "error"
            );
        }
    }

    public function show($id)
    {
        try {
            $query = BP::select([
                "DocEntry as id",
                "Name as bp_name"
            ])
                ->where('Status', '=', 'Y')
                ->where('DocEntry', '=', $id)
                ->first();

            return $this->responseApi([
                "data" => $query
            ], "Business Partner retrieved successfully");
        } catch (\Exception $exception) {
            return $this->responseApi(
                [
                    "data" => [],
                ],
                $exception->getMessage(),
                $exception->getCode(),
                "error"
            );
        }
    }

    public function store(Request $request)
    {
        $checkName = $this->checkName('', $request->Name);
        if ($checkName) {
            return response()->json([
                'errors' => true,
                'message' => 'Data axists! try another one!',
            ], 422);
        }
        try {
            $data = BP::create([
                'Name' => $request->Name,
                'Address' => $request->Address,
                'Status' => $request->Status ? $request->Status : 'Y',
                'Tenant' => (!empty($request->Tenant)) ? $request->Tenant : null,
                'Npwp1' => (!empty($request->Npwp1)) ? $request->Npwp1 : null,
                'Npwp2' => (!empty($request->Npwp2)) ? $request->Npwp2 : null,
                'Alias' => $request->Alias,
                'Created_by' => Auth::user()->username,
                'created_at' => Carbon::now(),
            ]);

            return $this->responseApi([
                "data" => $data
            ], "Business Partner retrieved successfully");
        } catch (\Exception $exception) {
            return $this->responseApi(
                [
                    "data" => [],
                ],
                $exception->getMessage(),
                $exception->getCode(),
                "error"
            );
        }
    }
    public function checkName($idp, $Name)
    {
        if ($idp == '') {
            $user = BP::where('Name', $Name)->first();
        } else {
            $user = BP::where('Name', $Name)->where('DocEntry', '!=', $idp)->first();
        }
        return $user ? true : false;
    }

    public function update(Request $request, $id)
    {
        try {
            $data = BP::where('DocEntry', $id)->first();
            if ($data) {
                try {
                    $data->Name = $request->Name;
                    $data->Address = $request->Address;
                    $data->Status = $request->Status;
                    $data->Tenant = (!empty($request->Tenant)) ? $request->Tenant : null;
                    $data->Npwp1 = (!empty($request->Npwp1)) ? $request->Npwp1 : null;
                    $data->Npwp2 = (!empty($request->Npwp2)) ? $request->Npwp2 : null;
                    $data->Alias = $request->Alias;
                    $data->updated_at = Carbon::now();
                    $update = $data->save();

                    return response()->json([
                        'errors' => false,
                        'message' => 'Data updated!',
                    ]);
                } catch (\Exception $e) {
                    return response()->json([
                        'errors' => true,
                        'message' => $e->getMessage(),
                    ], 422);
                }
            } else {
                return response()->json([
                    'errors' => true,
                    'message' => 'Data not found!',
                ], 422);
            }
        } catch (\Exception $e) {
            return response()->json([
                'errors' => true,
                'message' => $e->getMessage(),
            ], 422);
        }
    }
    public function destroy($id)
    {
        try {
            $data = BP::where('DocEntry', $id)->first();
            if ($data) {
                $data->delete();
                return response()->json([
                    'errors' => false,
                    'message' => 'Data deleted!',
                ]);
            } else {
                return response()->json([
                    'errors' => true,
                    'message' => 'Data not found!',
                ], 422);
            }
        } catch (\Exception $e) {
            return response()->json([
                'errors' => true,
                'message' => $e->getMessage(),
            ], 422);
        }
    }
}
