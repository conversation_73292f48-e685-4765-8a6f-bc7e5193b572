<?php

namespace Modules\ExternalApi\App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use Modules\ExternalApi\App\Traits\ApiResponseTrait;

class StoreBusinessPartnerRequest extends FormRequest
{
    use ApiResponseTrait;

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            "Name" => "required|string",
            "Address" => "required|string",
            "NPWP" => "required|string",
            "NITKU" => "required|string",
        ];
    }

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }
}
