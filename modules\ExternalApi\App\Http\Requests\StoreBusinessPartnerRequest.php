<?php

namespace Modules\ExternalApi\App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use Modules\ExternalApi\App\Traits\ApiResponseTrait;

class StoreBusinessPartnerRequest extends FormRequest
{
    use ApiResponseTrait;

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            "bp_name" => "required|string|unique:M_BP,Name",
            "address" => "required|string",
            "npwp" => "required|string",
            "nitku" => "required|string",
        ];
    }

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }
}
